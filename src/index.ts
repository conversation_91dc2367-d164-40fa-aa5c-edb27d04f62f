import { Hono } from 'hono';
import { contextStorage } from 'hono/context-storage';
import { cors } from 'hono/cors';
// import { openApiSpec } from './api/openapi';
// import { MCPWebSocketHandler } from './mcp/websocket-handler';
// Import middleware individually for debugging
import { apiKeyMiddleware } from './middleware/api-key-middleware';
import { authMiddleware } from './middleware/auth-middleware';
import { errorHandler } from './middleware/error-handler';
import { productionLogger, requestIdMiddleware, securityHeaders } from './middleware/production-logger';
// import {
//   apiKeyMiddleware,
//   authMiddleware,
//   errorHandler,
//   productionLogger,
//   requestIdMiddleware,
//   securityHeaders,
// } from './middleware/index';
// Import routes individually for debugging
// import { authApp } from './routes/auth-routes';
// Test individual service imports to isolate global scope issues
// import { billingService } from './services/billingService';
// import { paddleService } from './services/paddleService';
// import { paypalService } from './services/paypalService';
// import { default as billingApp } from './routes/billingApp';
// import { default as apiKeysApp } from './routes/apiKeysApp';
// import { documentApp } from './routes/document-routes';
// import { default as projectsApp } from './routes/projectsApp';
// import { searchApp } from './routes/search-routes';
// import { default as teamsApp } from './routes/teamsApp';
// import { uploadApp } from './routes/upload-routes';
import type { IHonoEnv } from './types';

const app = new Hono<IHonoEnv>();
// const mcpHandler = new MCPWebSocketHandler();

// Middleware
app.use(contextStorage());
app.use('*', errorHandler);
app.use('*', requestIdMiddleware);
app.use('*', productionLogger);
app.use('*', securityHeaders);
app.use(
  '*',
  cors({
    origin: [
      // Development domains
      'http://localhost:3000',
      'https://localhost:3000',
      'http://localhost:3050',
      'https://localhost:3050',
      'http://localhost:3060',
      'https://localhost:3060',
      // Production domains
      'https://ezcontext.dev',
      'https://app.ezcontext.dev',
      'https://api.ezcontext.dev',
    ],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
  })
);

// Authentication middleware (applied globally)
app.use('*', authMiddleware); // Session-based authentication
app.use('*', apiKeyMiddleware); // API key authentication

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    service: 'Collection Documentation Search API',
    version: '1.0.0-beta',
    status: 'running',
    endpoints: {
      rest_api: '/api/*',
      mcp_websocket: '/mcp/websocket',
      health: '/api/health',
      authentication: '/api/auth',
      teams: '/api/teams',
      projects: '/api/projects',
      api_keys: '/api/api-keys',
      search: '/api/search',
      file_upload: '/api/upload',
      document_management: '/api/documents',
      openapi_spec: '/openapi.json',
    },
    timestamp: new Date().toISOString(),
  });
});

// Mount REST API routes - Testing one by one
// app.route('/api/auth', authApp);
// app.route('/api/billing', billingApp);
// app.route('/api/teams', teamsApp);
// app.route('/api/projects', projectsApp);
// app.route('/api', apiKeysApp); // API keys routes include both user and team endpoints
// app.route('/api/search', searchApp);
// app.route('/api/upload', uploadApp);
// app.route('/api/documents', documentApp);

// MCP WebSocket endpoint - TEMPORARILY DISABLED FOR DEBUGGING
// app.get('/mcp/websocket', async (c) => {
//   return await mcpHandler.handleWebSocketUpgrade(c);
// });

// MCP connection status endpoint - TEMPORARILY DISABLED FOR DEBUGGING
// app.get('/mcp/status', async (c) => {
//   const { getEnv } = await import('./utils');
//   const env = await getEnv();

//   return c.json({
//     active_connections: mcpHandler.getConnectionCount(),
//     max_connections: parseInt(env.MCP_MAX_CONNECTIONS || '100'),
//     server_status: 'running',
//   });
// });

// OpenAPI specification endpoints - TEMPORARILY DISABLED FOR DEBUGGING
// app.get('/openapi.json', (c) => {
//   return c.json(openApiSpec);
// });

// app.get('/openapi.yaml', (c) => {
//   // Convert JSON to YAML (simple implementation)
//   const yamlString = JSON.stringify(openApiSpec, null, 2)
//     .replace(/"/g, '')
//     .replace(/,$/gm, '')
//     .replace(/^\s*[{}]/gm, '');

//   return c.text(yamlString, 200, {
//     'Content-Type': 'application/x-yaml',
//   });
// });

// API documentation endpoint
app.get('/docs', (c) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Library Documentation Search API</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
  <script>
    SwaggerUIBundle({
      url: '/openapi.json',
      dom_id: '#swagger-ui',
      presets: [
        SwaggerUIBundle.presets.apis,
        SwaggerUIBundle.presets.standalone
      ]
    });
  </script>
</body>
</html>`;

  return c.html(html);
});

export default app;
