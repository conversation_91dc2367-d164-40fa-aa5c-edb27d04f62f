/**
 * Projects Routes
 *
 * REST API endpoints for project management with team-based RBAC.
 * Provides CRUD operations, sharing, and collaboration features.
 */

import { zValidator } from '@hono/zod-validator';
// Import middleware individually to avoid global scope async operations
import { authMiddleware, requireAuth } from '@middleware/auth-middleware';
import { enforceApiRequestLimits } from '@middleware/billing-middleware';
import {
  type IUnifiedHonoEnv,
  requireAnyTeamPermission,
  requireTeamPermission,
  teamContextMiddleware,
} from '@middleware/rbac-middleware';
import { logProjectAction } from '@services/auditService';
import {
  createProject,
  deleteProject,
  getProjectWithDetails,
  getUserProjects,
  type ICreateProjectParams,
  type IProjectSharingParams,
  type IUpdateProjectParams,
  shareProjectWithTeam,
  unshareProject,
  updateProject,
} from '@services/projectService';
import { Hono } from 'hono';
import { z } from 'zod';

const app = new Hono<IUnifiedHonoEnv>();

// Apply authentication middleware to all routes
app.use('*', authMiddleware, requireAuth);

// Apply team context middleware to all routes that need team context for billing
app.use('*', teamContextMiddleware());

// ============================================================================
// Validation Schemas
// ============================================================================

const createProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  isPublic: z.boolean().optional(),
});

const updateProjectSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  isPublic: z.boolean().optional(),
});

const shareProjectSchema = z.object({
  teamId: z.string().uuid(),
});

// ============================================================================
// User Projects (Personal)
// ============================================================================

/**
 * GET /api/users/:userId/projects
 * Get user's personal projects
 */
app.get('/users/:userId/projects', enforceApiRequestLimits(), async (c) => {
  const user = c.get('user');
  const userId = c.req.param('userId');

  // Users can only access their own projects (or admins can access any)
  if (!user || (user.id !== userId && user.role !== 'admin')) {
    return c.json(
      {
        status: 'error',
        error: 'Access denied',
        error_type: 'authorization',
        timestamp: new Date().toISOString(),
      },
      403
    );
  }

  try {
    const projects = await getUserProjects(userId);

    return c.json({
      status: 'success',
      data: {
        projects,
        total: projects.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching user projects:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to fetch projects',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * POST /api/users/:userId/projects
 * Create a personal project
 */
app.post(
  '/users/:userId/projects',
  enforceApiRequestLimits(),
  zValidator('json', createProjectSchema),
  async (c) => {
    const user = c.get('user');
    const userId = c.req.param('userId');
    const body = c.req.valid('json');

    // Users can only create their own projects
    if (!user || user.id !== userId) {
      return c.json(
        {
          status: 'error',
          error: 'Access denied',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    try {
      const createParams: ICreateProjectParams = {
        userId,
        name: body.name,
        description: body.description,
        isPublic: body.isPublic,
      };

      const project = await createProject(createParams);

      // Log the action
      await logProjectAction(c, 'project.create', project.id, {
        project_name: body.name,
        is_public: body.isPublic,
      });

      return c.json(
        {
          status: 'success',
          data: {
            project,
          },
          timestamp: new Date().toISOString(),
        },
        201
      );
    } catch (error) {
      console.error('Error creating project:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create project',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Team Projects
// ============================================================================

/**
 * GET /api/teams/:teamId/projects
 * Get team's projects
 */
app.get(
  '/teams/:teamId/projects',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireAnyTeamPermission(['read.project', 'manage.project']),
  async (c) => {
    const user = c.get('user');
    const teamContext = c.get('teamContext');

    if (!user || !teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team membership required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    try {
      const projects = await getUserProjects(user.id, teamContext);

      return c.json({
        status: 'success',
        data: {
          projects,
          total: projects.length,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching team projects:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch team projects',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * POST /api/teams/:teamId/projects
 * Create a team project
 */
app.post(
  '/teams/:teamId/projects',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('manage.project'),
  zValidator('json', createProjectSchema),
  async (c) => {
    const user = c.get('user');
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    if (!user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    try {
      const createParams: ICreateProjectParams = {
        userId: user.id,
        teamId,
        name: body.name,
        description: body.description,
        isPublic: body.isPublic,
      };

      const project = await createProject(createParams);

      // Log the action
      await logProjectAction(c, 'project.create', project.id, {
        project_name: body.name,
        is_public: body.isPublic,
        team_id: teamId,
      });

      return c.json(
        {
          status: 'success',
          data: {
            project,
          },
          timestamp: new Date().toISOString(),
        },
        201
      );
    } catch (error) {
      console.error('Error creating team project:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create team project',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Individual Project Operations
// ============================================================================

/**
 * GET /api/projects/:projectId
 * Get specific project details
 */
app.get('/projects/:projectId', enforceApiRequestLimits(), async (c) => {
  const projectId = c.req.param('projectId');
  const user = c.get('user');
  const teamContext = c.get('teamContext');

  if (!user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const project = await getProjectWithDetails(projectId, user.id, teamContext || undefined);

    if (!project) {
      return c.json(
        {
          status: 'error',
          error: 'Project not found',
          error_type: 'not_found',
          timestamp: new Date().toISOString(),
        },
        404
      );
    }

    // Check if user has access to this project
    const hasAccess =
      project.userId === user.id || // Owner
      project.isPublic || // Public project
      (teamContext && project.teamId === teamContext.teamId) || // Team member
      user.role === 'admin'; // Admin

    if (!hasAccess) {
      return c.json(
        {
          status: 'error',
          error: 'Access denied',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    return c.json({
      status: 'success',
      data: {
        project,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to fetch project',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * PUT /api/projects/:projectId
 * Update project
 */
app.put(
  '/projects/:projectId',
  enforceApiRequestLimits(),
  zValidator('json', updateProjectSchema),
  async (c) => {
    const projectId = c.req.param('projectId');
    const user = c.get('user');
    const teamContext = c.get('teamContext');
    const body = c.req.valid('json');

    if (!user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    try {
      const updateParams: IUpdateProjectParams = {
        name: body.name,
        description: body.description,
        isPublic: body.isPublic,
      };

      const updatedProject = await updateProject(
        projectId,
        updateParams,
        user.id,
        teamContext || undefined
      );

      if (!updatedProject) {
        return c.json(
          {
            status: 'error',
            error: 'Project not found or access denied',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      // Log the action
      await logProjectAction(c, 'project.update', projectId, {
        old_values: {
          name: updatedProject.name,
          description: updatedProject.description,
        },
        new_values: body,
      });

      return c.json({
        status: 'success',
        data: {
          project: updatedProject,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error updating project:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to update project',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * DELETE /api/projects/:projectId
 * Delete project
 */
app.delete('/projects/:projectId', enforceApiRequestLimits(), async (c) => {
  const projectId = c.req.param('projectId');
  const user = c.get('user');
  const teamContext = c.get('teamContext');

  if (!user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const deleted = await deleteProject(projectId, user.id, teamContext || undefined);

    if (!deleted) {
      return c.json(
        {
          status: 'error',
          error: 'Project not found or access denied',
          error_type: 'not_found',
          timestamp: new Date().toISOString(),
        },
        404
      );
    }

    // Log the action
    await logProjectAction(c, 'project.delete', projectId, {
      project_id: projectId,
    });

    return c.json({
      status: 'success',
      data: {
        message: 'Project deleted successfully',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to delete project',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * POST /api/projects/:projectId/share
 * Share project with a team
 */
app.post(
  '/projects/:projectId/share',
  enforceApiRequestLimits(),
  zValidator('json', shareProjectSchema),
  async (c) => {
    const projectId = c.req.param('projectId');
    const user = c.get('user');
    const body = c.req.valid('json');

    if (!user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    try {
      // First check if user owns the project
      const project = await getProjectWithDetails(projectId, user.id);
      if (!project || !project.canShare) {
        return c.json(
          {
            status: 'error',
            error: 'Project not found or access denied',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      const sharingParams: IProjectSharingParams = {
        projectId,
        teamId: body.teamId,
        sharedBy: user.id,
      };

      const shared = await shareProjectWithTeam(sharingParams);

      if (!shared) {
        return c.json(
          {
            status: 'error',
            error: 'Failed to share project',
            error_type: 'database',
            timestamp: new Date().toISOString(),
          },
          500
        );
      }

      // Log the action
      await logProjectAction(c, 'project.share', projectId, {
        team_id: body.teamId,
        shared_by: user.id,
      });

      return c.json({
        status: 'success',
        data: {
          message: 'Project shared successfully',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error sharing project:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to share project',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * DELETE /api/projects/:projectId/share
 * Unshare project (remove from team)
 */
app.delete('/projects/:projectId/share', enforceApiRequestLimits(), async (c) => {
  const projectId = c.req.param('projectId');
  const user = c.get('user');

  if (!user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const unshared = await unshareProject(projectId, user.id);

    if (!unshared) {
      return c.json(
        {
          status: 'error',
          error: 'Project not found or access denied',
          error_type: 'not_found',
          timestamp: new Date().toISOString(),
        },
        404
      );
    }

    // Log the action
    await logProjectAction(c, 'project.unshare', projectId, {
      unshared_by: user.id,
    });

    return c.json({
      status: 'success',
      data: {
        message: 'Project unshared successfully',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error unsharing project:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to unshare project',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export default app;
